/**
 * Zdog SVG Viewer - Main viewer class for 3D SVG rendering
 */

import { Illus<PERSON>, Shape, Group, Anchor, Ellipse, Rect, Box } from 'zdog';
import { SVGParser } from './SVGParser';
import type {
  ZdogViewerOptions,
  ViewerControls,
  ViewerState,
  ViewerEvent,
  EventEmitter,
  ShapeProperties,
  ShapeUpdateOptions,
  ExportOptions,
  ZdogShape,
  ViewerConfiguration,
  PerformanceMetrics,
  ZdogViewerError,
  ZdogRenderError,
  InteractionState,
  PointerEvent as ZdogPointerEvent
} from './types/viewer-types';
import type { SVGImportOptions, ParsedSVGElement } from './types/svg-types';

export class ZdogSVGViewer implements EventEmitter<ViewerEvent> {
  private illustration: Illustration;
  private shapes: Map<string, ZdogShape> = new Map();
  private controls: ViewerControls;
  private animationFrameId: number | null = null;
  private eventListeners: Map<string, Array<(data: any) => void>> = new Map();
  private isDestroyed = false;
  private lastRenderTime = 0;
  private frameCount = 0;
  private fpsUpdateTime = 0;
  private currentFPS = 0;
  private svgParser: SVGParser;
  private interactionState: InteractionState;
  private container: HTMLElement;

  public readonly configuration: ViewerConfiguration = {
    performance: {
      maxShapes: 10000,
      targetFPS: 60,
      enableVirtualization: true,
      memoryLimit: 100 * 1024 * 1024 // 100MB
    },
    rendering: {
      antialiasing: true,
      shadows: false,
      wireframe: false,
      backgroundColor: '#f0f0f0'
    },
    interaction: {
      dragRotate: true,
      zoomSensitivity: 0.1,
      panSensitivity: 1.0,
      enableKeyboard: true
    },
    animation: {
      defaultDuration: 300,
      defaultEasing: 'ease-out',
      maxConcurrentAnimations: 10
    }
  };

  constructor(options: ZdogViewerOptions) {
    this.container = options.container;
    this.svgParser = new SVGParser();
    
    // Initialize controls
    this.controls = {
      autoRotate: false,
      rotationSpeed: 0.01,
      zoom: 1,
      panX: 0,
      panY: 0,
      depth: 20,
      strokeWidth: 2,
      showWireframe: false,
      enableShadows: false
    };

    // Initialize interaction state
    this.interactionState = {
      isDragging: false,
      isPanning: false,
      isZooming: false,
      lastPointer: { x: 0, y: 0 },
      dragStart: { x: 0, y: 0 },
      rotationStart: { x: 0, y: 0, z: 0 }
    };

    // Create Zdog illustration
    this.illustration = new Illustration({
      element: options.container,
      width: options.width || 800,
      height: options.height || 600,
      zoom: options.zoom || 1,
      dragRotate: options.dragRotate ?? true,
      resize: options.autoResize ?? true,
      onDragStart: this.handleDragStart.bind(this),
      onDragMove: this.handleDragMove.bind(this),
      onDragEnd: this.handleDragEnd.bind(this)
    });

    // Set background color
    if (options.backgroundColor) {
      this.setBackgroundColor(options.backgroundColor);
    }

    // Setup event listeners
    this.setupEventListeners();

    // Start render loop
    this.startRenderLoop();
  }

  /**
   * Get current viewer state
   */
  public get state(): ViewerState {
    return {
      shapes: new Map(this.shapes),
      controls: { ...this.controls },
      isAnimating: this.animationFrameId !== null,
      lastRenderTime: this.lastRenderTime,
      fps: this.currentFPS,
      isLoading: false,
      error: null
    };
  }

  /**
   * Load SVG content and convert to 3D shapes
   */
  public async loadSVG(svgContent: string, options: SVGImportOptions = {}): Promise<void> {
    try {
      this.emit({ type: 'load', data: { shapeCount: 0, loadTime: 0 } });
      
      const startTime = performance.now();
      
      // Clear existing shapes
      this.clearShapes();
      
      // Parse SVG
      const parseResult = this.svgParser.parseSVG(svgContent);
      
      if (parseResult.errors.length > 0) {
        console.warn('SVG parsing errors:', parseResult.errors);
      }

      // Convert SVG elements to Zdog shapes
      let shapeCount = 0;
      for (const element of parseResult.elements) {
        const zdogShape = await this.createZdogShape(element, options);
        if (zdogShape) {
          this.shapes.set(zdogShape.id, zdogShape);
          shapeCount++;
        }
      }

      // Center the view if requested
      if (options.centerOrigin) {
        this.centerView();
      }

      const loadTime = performance.now() - startTime;
      this.emit({ type: 'load', data: { shapeCount, loadTime } });
      
    } catch (error) {
      const zdogError = error instanceof ZdogViewerError ? error : 
        new ZdogViewerError(`Failed to load SVG: ${error}`, 'LOAD_ERROR');
      
      this.emit({ type: 'error', data: { error: zdogError } });
      throw zdogError;
    }
  }

  /**
   * Update shape properties
   */
  public updateShape(id: string, properties: Partial<ShapeProperties>, options: ShapeUpdateOptions = {}): void {
    const zdogShape = this.shapes.get(id);
    if (!zdogShape) {
      throw new ZdogViewerError(`Shape with id '${id}' not found`, 'SHAPE_NOT_FOUND');
    }

    try {
      // Update shape properties
      const shape = zdogShape.shape;
      
      if (properties.color !== undefined) {
        shape.color = properties.color;
      }
      
      if (properties.stroke !== undefined) {
        shape.stroke = properties.stroke;
      }
      
      if (properties.fill !== undefined) {
        shape.fill = properties.fill;
      }
      
      if (properties.visible !== undefined) {
        shape.visible = properties.visible;
      }
      
      if (properties.translate) {
        shape.translate.set(properties.translate);
      }
      
      if (properties.rotate) {
        shape.rotate.set(properties.rotate);
      }
      
      if (properties.scale) {
        shape.scale.set(properties.scale);
      }

      // Update stored properties
      Object.assign(zdogShape.properties, properties);

      // Emit update event
      this.emit({ type: 'shape-update', data: { shapeId: id, properties } });
      
    } catch (error) {
      throw new ZdogRenderError(`Failed to update shape '${id}': ${error}`, id);
    }
  }

  /**
   * Export current view as image
   */
  public async exportImage(options: ExportOptions): Promise<Blob> {
    try {
      const canvas = this.illustration.element as HTMLCanvasElement;
      if (!canvas || !(canvas instanceof HTMLCanvasElement)) {
        throw new ZdogViewerError('Canvas element not found', 'EXPORT_ERROR');
      }

      // Create export canvas with specified dimensions
      const exportCanvas = document.createElement('canvas');
      const exportCtx = exportCanvas.getContext('2d');
      if (!exportCtx) {
        throw new ZdogViewerError('Failed to get canvas context', 'EXPORT_ERROR');
      }

      const width = options.width || canvas.width;
      const height = options.height || canvas.height;
      const scale = options.scale || 1;

      exportCanvas.width = width * scale;
      exportCanvas.height = height * scale;

      // Set background color if specified
      if (options.backgroundColor) {
        exportCtx.fillStyle = options.backgroundColor;
        exportCtx.fillRect(0, 0, exportCanvas.width, exportCanvas.height);
      }

      // Draw current canvas content
      exportCtx.scale(scale, scale);
      exportCtx.drawImage(canvas, 0, 0, width, height);

      // Convert to blob
      return new Promise((resolve, reject) => {
        exportCanvas.toBlob(
          (blob) => {
            if (blob) {
              this.emit({ 
                type: 'export', 
                data: { 
                  format: options.format, 
                  size: blob.size, 
                  blob 
                } 
              });
              resolve(blob);
            } else {
              reject(new ZdogViewerError('Failed to create blob', 'EXPORT_ERROR'));
            }
          },
          `image/${options.format}`,
          options.quality || 0.9
        );
      });
      
    } catch (error) {
      const exportError = error instanceof ZdogViewerError ? error :
        new ZdogViewerError(`Export failed: ${error}`, 'EXPORT_ERROR');
      
      this.emit({ type: 'error', data: { error: exportError } });
      throw exportError;
    }
  }

  /**
   * Destroy the viewer and clean up resources
   */
  public destroy(): void {
    if (this.isDestroyed) return;

    // Stop animation loop
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    // Clear shapes
    this.clearShapes();

    // Remove event listeners
    this.eventListeners.clear();

    // Mark as destroyed
    this.isDestroyed = true;
  }

  /**
   * Event emitter implementation
   */
  public on(event: ViewerEvent['type'], handler: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(handler);
  }

  public off(event: ViewerEvent['type'], handler: (data: any) => void): void {
    const handlers = this.eventListeners.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  public emit(event: ViewerEvent): void {
    const handlers = this.eventListeners.get(event.type);
    if (handlers) {
      handlers.forEach(handler => handler(event.data));
    }
  }

  public once(event: ViewerEvent['type'], handler: (data: any) => void): void {
    const onceHandler = (data: any) => {
      handler(data);
      this.off(event, onceHandler);
    };
    this.on(event, onceHandler);
  }

  /**
   * Private methods
   */
  private async createZdogShape(element: ParsedSVGElement, options: SVGImportOptions): Promise<ZdogShape | null> {
    try {
      const id = element.id || `shape_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      let shape: Shape;
      
      switch (element.type) {
        case 'circle':
          shape = this.createCircleShape(element, options);
          break;
        case 'rect':
          shape = this.createRectShape(element, options);
          break;
        case 'path':
          shape = this.createPathShape(element, options);
          break;
        default:
          console.warn(`Unsupported shape type: ${element.type}`);
          return null;
      }

      // Apply transforms
      if (element.transform) {
        this.applyTransforms(shape, element.transform);
      }

      const properties: ShapeProperties = {
        color: element.style.fill || element.style.stroke || '#000000',
        stroke: element.style.strokeWidth || options.strokeWidth || this.controls.strokeWidth,
        fill: element.style.fill !== 'none',
        visible: true,
        translate: { x: 0, y: 0, z: 0 },
        rotate: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      };

      return {
        id,
        shape,
        originalElement: element,
        properties,
        bounds: element.bounds || { x: 0, y: 0, width: 0, height: 0 }
      };
      
    } catch (error) {
      console.error('Failed to create Zdog shape:', error);
      return null;
    }
  }

  private createCircleShape(element: ParsedSVGElement, options: SVGImportOptions): Shape {
    const bounds = element.bounds!;
    const radius = bounds.width / 2;
    
    return new Ellipse({
      addTo: this.illustration,
      diameter: radius * 2,
      stroke: options.strokeWidth || this.controls.strokeWidth,
      color: element.style.fill || element.style.stroke || '#000000',
      fill: element.style.fill !== 'none',
      translate: {
        x: bounds.x + radius,
        y: bounds.y + radius,
        z: 0
      }
    });
  }

  private createRectShape(element: ParsedSVGElement, options: SVGImportOptions): Shape {
    const bounds = element.bounds!;
    const depth = options.depth || this.controls.depth;
    
    return new Box({
      addTo: this.illustration,
      width: bounds.width,
      height: bounds.height,
      depth: depth,
      stroke: options.strokeWidth || this.controls.strokeWidth,
      color: element.style.fill || element.style.stroke || '#000000',
      fill: element.style.fill !== 'none',
      translate: {
        x: bounds.x + bounds.width / 2,
        y: bounds.y + bounds.height / 2,
        z: 0
      }
    });
  }

  private createPathShape(element: ParsedSVGElement, options: SVGImportOptions): Shape {
    // For now, create a simple shape - full path implementation would be more complex
    const bounds = element.bounds || { x: 0, y: 0, width: 10, height: 10 };
    
    return new Rect({
      addTo: this.illustration,
      width: bounds.width,
      height: bounds.height,
      stroke: options.strokeWidth || this.controls.strokeWidth,
      color: element.style.fill || element.style.stroke || '#000000',
      fill: element.style.fill !== 'none',
      translate: {
        x: bounds.x + bounds.width / 2,
        y: bounds.y + bounds.height / 2,
        z: 0
      }
    });
  }

  private applyTransforms(shape: Shape, transforms: any[]): void {
    // Apply SVG transforms to Zdog shape
    for (const transform of transforms) {
      switch (transform.type) {
        case 'translate':
          if (transform.values.length >= 2) {
            shape.translate.x += transform.values[0];
            shape.translate.y += transform.values[1];
          }
          break;
        case 'rotate':
          if (transform.values.length >= 1) {
            shape.rotate.z += transform.values[0] * Math.PI / 180;
          }
          break;
        case 'scale':
          if (transform.values.length >= 1) {
            const scale = transform.values[0];
            shape.scale.x *= scale;
            shape.scale.y *= scale;
            if (transform.values.length >= 2) {
              shape.scale.y *= transform.values[1] / scale;
            }
          }
          break;
      }
    }
  }

  private clearShapes(): void {
    for (const [id, zdogShape] of this.shapes) {
      zdogShape.shape.remove();
    }
    this.shapes.clear();
  }

  private centerView(): void {
    // Calculate bounds of all shapes and center the view
    if (this.shapes.size === 0) return;

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    for (const [, zdogShape] of this.shapes) {
      const bounds = zdogShape.bounds;
      minX = Math.min(minX, bounds.x);
      minY = Math.min(minY, bounds.y);
      maxX = Math.max(maxX, bounds.x + bounds.width);
      maxY = Math.max(maxY, bounds.y + bounds.height);
    }

    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;

    // Offset all shapes to center them
    for (const [, zdogShape] of this.shapes) {
      zdogShape.shape.translate.x -= centerX;
      zdogShape.shape.translate.y -= centerY;
    }
  }

  private setBackgroundColor(color: string): void {
    const canvas = this.illustration.element as HTMLCanvasElement;
    if (canvas && canvas instanceof HTMLCanvasElement) {
      canvas.style.backgroundColor = color;
    }
  }

  private setupEventListeners(): void {
    // Add keyboard event listeners if enabled
    if (this.configuration.interaction.enableKeyboard) {
      document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
  }

  private handleKeyDown(event: KeyboardEvent): void {
    // Handle keyboard shortcuts
    switch (event.key) {
      case 'r':
        this.controls.autoRotate = !this.controls.autoRotate;
        break;
      case 'w':
        this.controls.showWireframe = !this.controls.showWireframe;
        break;
    }
  }

  private handleDragStart(pointer: any): void {
    this.interactionState.isDragging = true;
    this.interactionState.dragStart = { x: pointer.x, y: pointer.y };
    this.interactionState.rotationStart = {
      x: this.illustration.rotate.x,
      y: this.illustration.rotate.y,
      z: this.illustration.rotate.z
    };
    
    this.emit({ 
      type: 'interaction', 
      data: { 
        type: 'drag', 
        delta: { x: 0, y: 0 } 
      } 
    });
  }

  private handleDragMove(pointer: any): void {
    if (!this.interactionState.isDragging) return;
    
    const deltaX = pointer.x - this.interactionState.dragStart.x;
    const deltaY = pointer.y - this.interactionState.dragStart.y;
    
    this.emit({ 
      type: 'interaction', 
      data: { 
        type: 'drag', 
        delta: { x: deltaX, y: deltaY } 
      } 
    });
  }

  private handleDragEnd(): void {
    this.interactionState.isDragging = false;
  }

  private startRenderLoop(): void {
    const render = (timestamp: number) => {
      if (this.isDestroyed) return;

      // Calculate FPS
      this.frameCount++;
      if (timestamp - this.fpsUpdateTime >= 1000) {
        this.currentFPS = Math.round((this.frameCount * 1000) / (timestamp - this.fpsUpdateTime));
        this.frameCount = 0;
        this.fpsUpdateTime = timestamp;
      }

      // Auto-rotate if enabled
      if (this.controls.autoRotate) {
        this.illustration.rotate.y += this.controls.rotationSpeed;
      }

      // Update and render
      this.illustration.updateRenderGraph();
      
      this.lastRenderTime = timestamp;
      
      // Emit render event
      this.emit({ 
        type: 'render', 
        data: { 
          fps: this.currentFPS, 
          renderTime: performance.now() - timestamp 
        } 
      });

      this.animationFrameId = requestAnimationFrame(render);
    };

    this.animationFrameId = requestAnimationFrame(render);
  }
}
