/**
 * Zdog 3D SVG Viewer - Main Export File
 * 
 * This file exports all the main components, types, and utilities
 * for the Zdog 3D SVG viewer implementation.
 */

// Core Classes
export { ZdogSVGViewer } from './ZdogViewer';
export { SVGParser } from './SVGParser';
export { ZdogExporter } from './ZdogExporter';

// Vue Components
export { default as ZdogControls } from './ZdogControls.vue';
export { default as EditorViewer } from '../integration/EditorViewer.vue';

// Performance Utilities
export { 
  PerformanceOptimizer, 
  ObjectPool, 
  debounce, 
  throttle 
} from './utils/performance';

// Type Definitions
export type {
  // Viewer Types
  ZdogViewerOptions,
  ViewerControls,
  ViewerState,
  ViewerEvent,
  EventEmitter,
  ShapeProperties,
  ShapeUpdateOptions,
  ExportOptions,
  ZdogShape,
  ViewerConfiguration,
  PerformanceMetrics,
  InteractionState,
  PointerEvent as ZdogPointerEvent,
  Camera,
  RenderOptions,
  
  // Error Types
  ZdogViewerError,
  ZdogRenderError,
  ZdogAnimationError,
  
  // Plugin Types
  ZdogPlugin,
  PluginManager
} from './types/viewer-types';

export type {
  // SVG Types
  SVGPathCommand,
  SVGPoint,
  SVGBounds,
  SVGTransform,
  SVGStyle,
  ParsedSVGElement,
  SVGParseOptions,
  SVGImportOptions,
  SVGParseResult,
  SVGParseError,
  SVGRenderError,
  ParsedPathSegment,
  CubicBezierSegment,
  QuadraticBezierSegment,
  ArcSegment,
  
  // Color Types
  ColorRGB,
  ColorHSL,
  ParsedColor,
  
  // Utility Types
  SVGProcessor,
  SVGOptimizer,
  PathCommandParser,
  PathSegment
} from './types/svg-types';

// Zdog Type Re-exports
export type {
  Vector,
  VectorOptions,
  Anchor,
  AnchorOptions,
  Group,
  GroupOptions,
  Shape,
  ShapeOptions,
  Illustration,
  IllustrationOptions
} from './types/zdog.d';

// Example SVGs
export { 
  testSVGs, 
  getRandomTestSVG, 
  getAllTestSVGs 
} from './examples/test-svgs';

// Geometry Utilities
export {
  distance2D,
  distance3D,
  lerp2D,
  lerp3D,
  normalize3D,
  cross3D,
  dot3D,
  calculateBoundingBox,
  degToRad,
  radToDeg,
  clamp,
  rotationMatrixX,
  rotationMatrixY,
  rotationMatrixZ,
  transformPoint3D,
  pointInBoundingBox
} from '../utils/geometry';

export type {
  Point2D,
  Point3D,
  Matrix3D,
  BoundingBox
} from '../utils/geometry';

// Color Utilities
export {
  parseColor,
  parseHexColor,
  parseRgbColor,
  parseHslColor,
  parseNamedColor,
  rgbToHex,
  hslToRgb,
  rgbToHsl,
  lightenColor,
  darkenColor,
  getContrastColor,
  blendColors,
  randomColor,
  isValidColor
} from '../utils/color';

export type {
  RGB,
  RGBA,
  HSL,
  HSLA
} from '../utils/color';

// Constants
export const ZDOG_VIEWER_VERSION = '1.0.0';

export const DEFAULT_VIEWER_OPTIONS: Partial<ZdogViewerOptions> = {
  width: 800,
  height: 600,
  backgroundColor: '#f0f0f0',
  dragRotate: true,
  autoResize: true,
  zoom: 1
};

export const DEFAULT_IMPORT_OPTIONS: Partial<SVGImportOptions> = {
  depth: 20,
  strokeWidth: 2,
  preserveAspectRatio: true,
  scale: 1,
  centerOrigin: true,
  extrudeMode: 'uniform'
};

export const DEFAULT_EXPORT_OPTIONS: Partial<ExportOptions> = {
  format: 'png',
  quality: 0.9,
  scale: 1
};

// Utility Functions
export const createViewer = (
  container: HTMLElement, 
  options: Partial<ZdogViewerOptions> = {}
): ZdogSVGViewer => {
  return new ZdogSVGViewer({
    container,
    ...DEFAULT_VIEWER_OPTIONS,
    ...options
  });
};

export const loadSVGFromString = async (
  viewer: ZdogSVGViewer,
  svgContent: string,
  options: Partial<SVGImportOptions> = {}
): Promise<void> => {
  return viewer.loadSVG(svgContent, {
    ...DEFAULT_IMPORT_OPTIONS,
    ...options
  });
};

export const exportViewerImage = async (
  viewer: ZdogSVGViewer,
  options: Partial<ExportOptions> = {}
): Promise<Blob> => {
  return viewer.exportImage({
    ...DEFAULT_EXPORT_OPTIONS,
    ...options
  });
};

// Validation Functions
export const isValidSVG = (content: string): boolean => {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'image/svg+xml');
    const parserError = doc.querySelector('parsererror');
    return !parserError && !!doc.querySelector('svg');
  } catch {
    return false;
  }
};

export const validateViewerOptions = (options: ZdogViewerOptions): string[] => {
  const errors: string[] = [];
  
  if (!options.container) {
    errors.push('Container element is required');
  }
  
  if (options.width && options.width <= 0) {
    errors.push('Width must be positive');
  }
  
  if (options.height && options.height <= 0) {
    errors.push('Height must be positive');
  }
  
  if (options.zoom && options.zoom <= 0) {
    errors.push('Zoom must be positive');
  }
  
  return errors;
};

// Error Classes (re-export for convenience)
export { 
  ZdogViewerError, 
  ZdogRenderError, 
  ZdogAnimationError 
} from './types/viewer-types';

export { 
  SVGParseError, 
  SVGRenderError 
} from './types/svg-types';

// Browser Compatibility Check
export const checkBrowserCompatibility = (): { 
  compatible: boolean; 
  missing: string[] 
} => {
  const missing: string[] = [];
  
  if (!window.requestAnimationFrame) {
    missing.push('requestAnimationFrame');
  }
  
  if (!window.DOMParser) {
    missing.push('DOMParser');
  }
  
  if (!HTMLCanvasElement.prototype.toBlob) {
    missing.push('Canvas.toBlob');
  }
  
  if (!window.Worker) {
    missing.push('Web Workers');
  }
  
  return {
    compatible: missing.length === 0,
    missing
  };
};

// Performance Monitoring
export const createPerformanceMonitor = () => {
  let frameCount = 0;
  let lastTime = performance.now();
  let fps = 0;
  
  const update = () => {
    frameCount++;
    const currentTime = performance.now();
    
    if (currentTime - lastTime >= 1000) {
      fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
      frameCount = 0;
      lastTime = currentTime;
    }
    
    requestAnimationFrame(update);
  };
  
  update();
  
  return {
    getFPS: () => fps,
    getMemoryUsage: () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        return Math.round(memory.usedJSHeapSize / 1024 / 1024);
      }
      return 0;
    }
  };
};

// Development Helpers
export const enableDebugMode = (viewer: ZdogSVGViewer): void => {
  // Add debug information to the viewer
  (window as any).__ZDOG_VIEWER_DEBUG__ = {
    viewer,
    state: viewer.state,
    version: ZDOG_VIEWER_VERSION
  };
  
  console.log('Zdog Viewer Debug Mode Enabled');
  console.log('Access viewer via window.__ZDOG_VIEWER_DEBUG__');
};

// Version Information
export const getVersionInfo = () => ({
  version: ZDOG_VIEWER_VERSION,
  buildDate: new Date().toISOString(),
  dependencies: {
    zdog: '^1.1.3'
  }
});

// Default export for convenience
export default {
  ZdogSVGViewer,
  SVGParser,
  ZdogExporter,
  createViewer,
  loadSVGFromString,
  exportViewerImage,
  isValidSVG,
  checkBrowserCompatibility,
  version: ZDOG_VIEWER_VERSION
};
